{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 3635516094190571389], [6416823254013318197, "build_script_build", false, 1675398300601831102], [16171925541490437305, "build_script_build", false, 16980917883343827751]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-http-4c31d030f0396643\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}