{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"protocol-asset\"]", "target": 12335857691995296458, "profile": 8889149410476752835, "path": 988425117594393849, "deps": [[65234016722529558, "bincode", false, 14657189225826523957], [2779309023524819297, "aho_corasick", false, 11226609090019059295], [5986029879202738730, "log", false, 5199913866023184003], [6416823254013318197, "tauri_plugin_fs", false, 6273905065782412068], [9689903380558560274, "serde", false, 8088836602735757304], [10806645703491011684, "thiserror", false, 11072587440867868533], [14039947826026167952, "tauri", false, 4889537979957540148], [15367738274754116744, "serde_json", false, 7025531498439238694]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-persisted-scope-b2b462ea64527b80\\dep-lib-tauri_plugin_persisted_scope", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}