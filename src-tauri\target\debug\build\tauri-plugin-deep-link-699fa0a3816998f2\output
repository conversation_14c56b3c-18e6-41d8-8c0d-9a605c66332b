cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\repos\quadrant\src-tauri\target\debug\build\tauri-plugin-deep-link-699fa0a3816998f2\out\tauri-plugin-deep-link-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-deep-link-2.4.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rerun-if-env-changed=TAURI_DEEP_LINK_PLUGIN_CONFIG
