cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=D:\repos\quadrant\src-tauri\tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=mcmodpackmanager
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=dev_mrquantumoff
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=D:\repos\quadrant\src-tauri\target\debug\build\quadrant_next-97ce0c391e3714e1\out\app-manifest\__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-pc-windows-msvc
cargo:rerun-if-changed=..\public\Inter-Font.ttf
cargo:rerun-if-changed=..\public\logo.png
cargo:rerun-if-changed=..\public\logo.svg
cargo:rerun-if-changed=..\public\logoNoBg.png
cargo:rerun-if-changed=..\public\logoNoBg.svg
cargo:rerun-if-changed=..\public\tray.png
cargo:rerun-if-changed=..\public\tray.svg
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=D:\repos\quadrant\src-tauri\target\debug\build\quadrant_next-97ce0c391e3714e1\out\resource.lib
