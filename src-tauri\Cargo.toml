[package]
name = "quadrant_next"
version = "25.6.0-stable"
description = "An easy way to manage your Minecraft mods and modpacks"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "quadrant_next_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[features]
default = []
telemetry = []
curseforge = []
# Quadrant ID/Sync/Share
quadrant_id = []
proprietary = ["telemetry", "curseforge", "quadrant_id"]


[build-dependencies]
tauri-build = { version = "2.3.0", features = [] }


[dependencies]

tauri = { version = "2.6.2", features = ["tray-icon", "config-json5"] }

tauri-plugin-opener = "2.4.0"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
chrono = { version = "0.4.41", features = ["serde"] }
anyhow = { version = "1.0.98" }
dirs = "6.0.0"
log = "0.4.27"
tauri-plugin-fs = { version = "2.4.0", features = ["watch"] }
tauri-plugin-os = "2.3.0"
rss = "2.0.12"
tauri-plugin-store = "2.3.0"
uuid = { version = "1.17.0", features = ["v7"] }
tauri-plugin-http = "2.5.0"
tokio = { version = "1.46.1", features = ["full"] }
urlencoding = "2.1.3"
open = "5.3.2"
futures = { version = "0.3.31" }
sha1 = "0.10.6"
hex = "0.4.3"
colog = "1.3.0"
tauri-plugin-deep-link = "2.4.0"
tauri-plugin-dialog = "2.3.0"
keyring = { version = "3.6.2", features = [
    "apple-native",
    "windows-native",
    "sync-secret-service",
] }
tauri-plugin-clipboard-manager = "2.3.0"
tauri-plugin-notification = "2.3.0"
tauri-plugin-persisted-scope = "2.2.2"
http-cache-reqwest = { version = "0.16.0", default-features = false, features = [
    "manager-moka",
] }
reqwest-middleware = "0.4.2"
tauri-plugin-oauth = "2"
zip = "4.0.0"


[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-autostart = "2.3.0"
tauri-plugin-cli = "2.2.0"
tauri-plugin-updater = "2.7.1"
tauri-plugin-single-instance = { version = "2.2.4", features = ["deep-link"] }

[profile.dev]
opt-level = 0
overflow-checks = false

[profile.release]
opt-level = 3
overflow-checks = true
codegen-units = 1
lto = "fat"
