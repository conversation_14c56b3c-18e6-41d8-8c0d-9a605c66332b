{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 8889149410476752835, "path": 17599426264896507594, "deps": [[40386456601120721, "percent_encoding", false, 3031576213882341751], [1200537532907108615, "url<PERSON><PERSON>n", false, 2653451681562413474], [2013030631243296465, "webview2_com", false, 17953565385658985206], [2671782512663819132, "tauri_utils", false, 7404016811302967800], [3150220818285335163, "url", false, 4984914845944321216], [4143744114649553716, "raw_window_handle", false, 14544629153134257825], [4494683389616423722, "muda", false, 3416573352238414404], [4919829919303820331, "serialize_to_javascript", false, 15890391851608097616], [5010121536552982683, "getrandom", false, 17474357057633497816], [5986029879202738730, "log", false, 5199913866023184003], [6089812615193535349, "tauri_runtime", false, 13522824735088400325], [7573826311589115053, "tauri_macros", false, 15588348017113592826], [9010263965687315507, "http", false, 16240634061175394875], [9557679338812073212, "tray_icon", false, 3552705801667743882], [9689903380558560274, "serde", false, 8088836602735757304], [10229185211513642314, "mime", false, 13891297091779489551], [10806645703491011684, "thiserror", false, 11072587440867868533], [11599800339996261026, "tauri_runtime_wry", false, 16334571210822307489], [11989259058781683633, "dunce", false, 11138545899030231032], [12393800526703971956, "tokio", false, 2515388709007687167], [12565293087094287914, "window_vibrancy", false, 15126619650777416853], [12986574360607194341, "serde_repr", false, 14701694648897431950], [13077543566650298139, "heck", false, 11795220262428474449], [13116089016666501665, "windows", false, 11559965676104421625], [13625485746686963219, "anyhow", false, 18400502117599137916], [14039947826026167952, "build_script_build", false, 3635516094190571389], [15367738274754116744, "serde_json", false, 7025531498439238694], [16928111194414003569, "dirs", false, 2356795138224988605], [17155886227862585100, "glob", false, 15048036430121811082]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-23aa0bb516147ef9\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}