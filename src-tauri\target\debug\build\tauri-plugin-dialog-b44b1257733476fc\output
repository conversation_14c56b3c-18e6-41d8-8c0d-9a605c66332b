cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\repos\quadrant\src-tauri\target\debug\build\tauri-plugin-dialog-b44b1257733476fc\out\tauri-plugin-dialog-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-dialog-2.3.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
