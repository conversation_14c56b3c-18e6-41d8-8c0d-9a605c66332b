{"rustc": 16591470773350601817, "features": "[\"default\", \"rustls-tls\", \"zip\"]", "declared_features": "[\"default\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"zip\"]", "target": 5081136802505358982, "profile": 8889149410476752835, "path": 8226979608388589612, "deps": [[40386456601120721, "percent_encoding", false, 3031576213882341751], [1432378608212364556, "minisign_verify", false, 12016107349194938780], [3150220818285335163, "url", false, 4984914845944321216], [4843756016100095416, "build_script_build", false, 5431649430824937561], [4899080583175475170, "semver", false, 1081609954718456312], [5986029879202738730, "log", false, 5199913866023184003], [6873154789535483674, "zip", false, 7230146740074976932], [7263319592666514104, "windows_sys", false, 9936525085284655179], [9010263965687315507, "http", false, 16240634061175394875], [9689903380558560274, "serde", false, 8088836602735757304], [10629569228670356391, "futures_util", false, 9745542081346755986], [10806645703491011684, "thiserror", false, 11072587440867868533], [12261610614527126074, "tempfile", false, 12382807938533308920], [12393800526703971956, "tokio", false, 2515388709007687167], [12409575957772518135, "time", false, 9970639226396888473], [13077212702700853852, "base64", false, 3563282865695654342], [14039947826026167952, "tauri", false, 4889537979957540148], [15367738274754116744, "serde_json", false, 7025531498439238694], [16593743942913858012, "reqwest", false, 13431200881376758808], [17146114186171651583, "infer", false, 17675484393689654080]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-updater-7a1e95246d1555b9\\dep-lib-tauri_plugin_updater", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}