{"rustc": 16591470773350601817, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 8889149410476752835, "path": 13721629779228933147, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 2653451681562413474], [3150220818285335163, "url", false, 4984914845944321216], [6416823254013318197, "tauri_plugin_fs", false, 6273905065782412068], [8298091525883606470, "cookie_store", false, 10350396775698834142], [9010263965687315507, "http", false, 16240634061175394875], [9451456094439810778, "regex", false, 2897801170384316550], [9689903380558560274, "serde", false, 8088836602735757304], [10806645703491011684, "thiserror", false, 11072587440867868533], [12393800526703971956, "tokio", false, 2515388709007687167], [14039947826026167952, "tauri", false, 4889537979957540148], [15367738274754116744, "serde_json", false, 7025531498439238694], [16066129441945555748, "bytes", false, 10466300400805285687], [16171925541490437305, "build_script_build", false, 9881088200448386785], [16593743942913858012, "reqwest", false, 13431200881376758808], [17047088963840213854, "data_url", false, 10661061021314167823]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-06c8633810cd6e73\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}