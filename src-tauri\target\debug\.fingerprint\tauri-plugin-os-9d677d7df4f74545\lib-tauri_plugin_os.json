{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 11842472800422696103, "profile": 8889149410476752835, "path": 1014791734817888990, "deps": [[3105261289537770242, "build_script_build", false, 13368395361628961295], [4919829919303820331, "serialize_to_javascript", false, 15890391851608097616], [5986029879202738730, "log", false, 5199913866023184003], [8403448927100847669, "gethostname", false, 11276234632002887380], [9689903380558560274, "serde", false, 8088836602735757304], [10806645703491011684, "thiserror", false, 11072587440867868533], [12042770108345393747, "os_info", false, 18229611535106085545], [14039947826026167952, "tauri", false, 4889537979957540148], [14618885535728128396, "sys_locale", false, 1237165838323117324], [15367738274754116744, "serde_json", false, 7025531498439238694]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-os-9d677d7df4f74545\\dep-lib-tauri_plugin_os", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}