{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 8889149410476752835, "path": 3896319221372960722, "deps": [[947818755262499932, "notify_rust", false, 13020621317370198149], [3150220818285335163, "url", false, 4984914845944321216], [5986029879202738730, "log", false, 5199913866023184003], [7760050409050412348, "build_script_build", false, 9126554425039555922], [9689903380558560274, "serde", false, 8088836602735757304], [10806645703491011684, "thiserror", false, 11072587440867868533], [12409575957772518135, "time", false, 9970639226396888473], [12986574360607194341, "serde_repr", false, 14701694648897431950], [13208667028893622512, "rand", false, 8428321506366688979], [14039947826026167952, "tauri", false, 4889537979957540148], [15367738274754116744, "serde_json", false, 7025531498439238694]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-notification-a93c61a67e32977c\\dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}