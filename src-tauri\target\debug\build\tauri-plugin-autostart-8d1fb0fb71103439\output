cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\repos\quadrant\src-tauri\target\debug\build\tauri-plugin-autostart-8d1fb0fb71103439\out\tauri-plugin-autostart-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-autostart-2.5.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
