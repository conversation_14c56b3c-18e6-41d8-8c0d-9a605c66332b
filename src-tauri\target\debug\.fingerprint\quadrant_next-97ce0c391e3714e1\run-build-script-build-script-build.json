{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[16829730753633342846, "build_script_build", false, 13438803300447032855], [14039947826026167952, "build_script_build", false, 3635516094190571389], [1496950008570377947, "build_script_build", false, 3736856835879004473], [2979645065415301498, "build_script_build", false, 17976560352373536781], [1797035611096599003, "build_script_build", false, 15372580846870068492], [5719423723759041893, "build_script_build", false, 11542036126704280634], [14525517306681678134, "build_script_build", false, 10347086835064552361], [6416823254013318197, "build_script_build", false, 1675398300601831102], [16171925541490437305, "build_script_build", false, 9881088200448386785], [7760050409050412348, "build_script_build", false, 9126554425039555922], [7969598796803312533, "build_script_build", false, 2886454275272859696], [16702348383442838006, "build_script_build", false, 10446632319563537066], [3105261289537770242, "build_script_build", false, 13368395361628961295], [2528501548713217016, "build_script_build", false, 15142800991534591283], [4843756016100095416, "build_script_build", false, 5431649430824937561]], "local": [{"RerunIfChanged": {"output": "debug\\build\\quadrant_next-97ce0c391e3714e1\\output", "paths": ["tauri.conf.json", "capabilities", "..\\public\\Inter-Font.ttf", "..\\public\\logo.png", "..\\public\\logo.svg", "..\\public\\logoNoBg.png", "..\\public\\logoNoBg.svg", "..\\public\\tray.png", "..\\public\\tray.svg"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}